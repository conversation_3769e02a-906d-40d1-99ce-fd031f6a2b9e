import { layers, namedFlavor } from '@protomaps/basemaps';

// Tile paths
const sgTilesPath = new URL('../tiles/singapore.pmtiles', import.meta.url);
const sgBuildingsTilesPath = new URL(
  '../tiles/singapore-buildings.pmtiles',
  import.meta.url,
);
const sgRailTilesPath = new URL('../tiles/sg-rail.geojson', import.meta.url);

const GLYPHS_URL =
  'https://protomaps.github.io/basemaps-assets/fonts/{fontstack}/{range}.pbf';
const SPRITE_URL =
  'https://protomaps.github.io/basemaps-assets/sprites/v4/light';

// Customize Protomaps light theme
const currentFlavor = namedFlavor('light');
const flavor = {
  ...currentFlavor,
  background: '#bee8f9',
  water: '#bee8f9',
  earth: '#f8f8f3',
  landcover: {
    ...currentFlavor.landcover,
    farmland: '#deecd5',
    forest: '#deecd5',
    grassland: '#deecd5',
  },
  buildings: '#f0f0ee',
  hospital: '#fcf3f2',
  park_a: '#deecd5',
  park_b: '#deecd5',
  wood_a: '#deecd5',
  wood_b: '#deecd5',
  scrub_a: '#deecd5',
  scrub_b: '#deecd5',
  aerodrome: '#e9eaf2',
  industrial: '#f8f8f3',
  military: '#f8f8f3',
  zoo: '#deecd5',
  minor_a: '#fff',
  minor_service: '#fff',
  school: '#fbf0ea',
  pedestrian: '#f3f2eb',

  tunnel_other: '#e5e5d2',
  tunnel_minor: '#e5e5d2',
  tunnel_link: '#e5e5d2',
  tunnel_major: '#e5e5d2',
  tunnel_highway: '#e5e5d2',

  bridges_major: '#fff',

  subplace_label_halo: '#fff',
  city_label_halo: '#fff',
  state_label_halo: '#fff',
};

export function createMapStyle({ lang = 'en' } = {}) {
  const mapLayers = layers('protomaps', flavor, {
    lang,
  });

  // Process all layers in a single loop - modify, remove, and track
  const layersToRemove = ['address_label', 'roads_rail'];
  const customMapLayers = [];
  let poisLayer = null;

  for (const layer of mapLayers) {
    // Skip layers that should be removed
    if (layersToRemove.includes(layer.id)) {
      continue;
    }

    // Modify layers based on their ID or pattern
    switch (layer.id) {
      case 'buildings':
        // Replace buildings with Overture buildings
        layer.filter = ['!=', ['get', 'is_underground'], true];
        layer.source = 'buildings';
        layer['source-layer'] = 'building';
        layer.paint['fill-outline-color'] = '#d7d7c7';
        layer.paint['fill-opacity'] = 1;
        layer.minzoom = 16;
        break;

      case 'places_subplace':
        layer.layout['text-font'] = ['Noto Sans Medium'];
        layer.paint['text-halo-width'] = 2;
        layer.minzoom = 10;
        break;

      case 'places_locality':
        layer.minzoom = 14;
        break;

      case 'pois':
        poisLayer = layer;
        layer.minzoom = 16;
        // Re-adjust the kind filters in pois
        const poisFilterKindsLiteral = layer.filter
          ?.find?.((v) => v[0] === 'in')
          ?.find((v) => v[0] === 'literal');
        const poisFilterKinds = poisFilterKindsLiteral?.[1];
        console.log('KINDS', layer.filter, poisFilterKinds);
        if (poisFilterKinds?.length) {
          poisFilterKindsLiteral[1] = [
            'aerodrome',
            'animal',
            // 'arrow',
            'beach',
            // 'bench',
            // 'bus_stop',
            'capital',
            // 'drinking_water',
            'ferry_terminal',
            'forest',
            'garden',
            'library',
            'marina',
            'park',
            'peak',
            'school',
            'stadium',
            // 'toilets',
            'townspot',
            // 'train_station',
            'university',
            'zoo',
          ];
        }
        break;
    }

    // Apply pattern-based modifications
    if (/(water|road).+label/i.test(layer.id)) {
      layer.minzoom = 16;
    }

    if (/road.+label/i.test(layer.id)) {
      layer.layout['text-transform'] = 'uppercase';
    }

    // Add the layer to our custom array
    customMapLayers.push(layer);

    // Add buildings_label after pois
    if (layer.id === 'pois' && poisLayer) {
      customMapLayers.push({
        id: 'buildings_label',
        type: 'symbol',
        source: 'buildings',
        'source-layer': 'building',
        minzoom: 15,
        layout: {
          'text-field': ['get', '@name'],
          'text-font': ['Noto Sans Regular'],
          'text-max-width': 8,
          'text-size': poisLayer.layout['text-size'],
          'text-padding': 8,
        },
        paint: {
          ...poisLayer.paint,
          'text-color': currentFlavor.pois.slategray,
        },
      });
    }
  }

  console.log(
    'LAYERS',
    customMapLayers.map((l) => l.id),
  );

  const mapStyle = {
    version: 8,
    glyphs: GLYPHS_URL,
    sprite: SPRITE_URL,
    sources: {
      protomaps: {
        type: 'vector',
        url: `pmtiles://${sgTilesPath}`,
        attribution:
          '<a href="https://protomaps.com" target="_blank">Protomaps</a> © <a href="https://openstreetmap.org" target="_blank">OpenStreetMap</a>',
      },
      buildings: {
        type: 'vector',
        url: `pmtiles://${sgBuildingsTilesPath}`,
        // Don't need OSM because already covered by the one above
        attribution:
          '<a href="https://overturemaps.org" target="_blank">Overture Maps Foundation</a>',
      },
      'sg-rail': {
        type: 'geojson',
        data: sgRailTilesPath.href,
        attribution:
          '© <a href="https://www.smrt.com.sg/" target="_blank" title="Singapore Mass Rapid Transit">SMRT</a> © <a href="https://www.sbstransit.com.sg/" target="_blank" title="Singapore Bus Services">SBS</a>',
      },
    },
    layers: customMapLayers,
  };

  return mapStyle;
}

export { sgRailTilesPath };
